import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/settings_page/advanced.dart';
import 'package:dasso_reader/page/settings_page/ai.dart';
import 'package:dasso_reader/page/settings_page/appearance.dart';
import 'package:dasso_reader/page/settings_page/narrate.dart';
import 'package:dasso_reader/page/settings_page/reading.dart';
import 'package:dasso_reader/page/settings_page/settings_page.dart';
import 'package:dasso_reader/page/settings_page/storege.dart';
import 'package:dasso_reader/page/settings_page/sync.dart';
import 'package:dasso_reader/page/settings_page/translate.dart';
import 'package:dasso_reader/page/settings_page/chinese_learning.dart';
import 'package:dasso_reader/widgets/settings/about.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

class MoreSettings extends StatelessWidget {
  const MoreSettings({super.key});

  @override
  Widget build(BuildContext context) {
    return SemanticHelpers.listItem(
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceXS, // Optimized vertical space
        ),
        leading: Icon(
          Icons.settings_outlined,
          size: DesignSystem.getAdjustedIconSize(24.0),
          semanticLabel: 'More settings icon',
        ),
        title: Text(
          L10n.of(context).settings_moreSettings,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
          ),
        ),
        trailing: Icon(
          AdaptiveIcons.chevronRight,
          size: DesignSystem.getAdjustedIconSize(20.0),
          semanticLabel: 'Navigate to more settings',
        ),
        onTap: () {
          Navigator.push(
            context,
            NavigationSystem.createTransition(
              page: const SubMoreSettings(),
              settings: const RouteSettings(name: '/more-settings'),
              type: NavigationTransitionType.platform,
            ),
          );
        },
      ),
      label: 'More settings',
      hint: 'Tap to access additional settings categories',
      onTap: () {
        Navigator.push(
          context,
          NavigationSystem.createTransition(
            page: const SubMoreSettings(),
            settings: const RouteSettings(name: '/more-settings'),
            type: NavigationTransitionType.platform,
          ),
        );
      },
    );
  }
}

class SubMoreSettings extends StatefulWidget {
  const SubMoreSettings({super.key});

  @override
  State<SubMoreSettings> createState() => _SubMoreSettingsState();
}

class _SubMoreSettingsState extends State<SubMoreSettings> {
  int selectedIndex = 0;
  Widget? settingsDetail;

  // Performance optimization: Cache settings list to avoid rebuilding
  List<Map<String, dynamic>>? _cachedSettings;

  // Performance optimization: Lazy loading for settings sections
  final Map<int, Widget> _settingsSectionCache = {};

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Pre-cache the settings list for better performance
    _cacheSettingsList();
  }

  @override
  void dispose() {
    // Clear caches to prevent memory leaks
    _settingsSectionCache.clear();
    super.dispose();
  }

  /// Cache the settings list to avoid rebuilding on every build
  void _cacheSettingsList() {
    if (_cachedSettings != null) return;

    _cachedSettings = [
      {
        "title": L10n.of(context).settings_appearance,
        "icon": Icons.palette_outlined,
        "sections": const AppearanceSetting(),
        "subtitles": [
          L10n.of(context).settings_appearance_theme,
          L10n.of(context).settings_appearance_display,
          L10n.of(context).settings_bookshelf_cover,
        ],
      },
      {
        "title": L10n.of(context).settings_reading,
        "icon": Icons.menu_book_outlined,
        "sections": const ReadingSettings(),
        "subtitles": [
          L10n.of(context).reading_page_reading,
          L10n.of(context).download_fonts,
          L10n.of(context).reading_page_style,
          L10n.of(context).reading_page_other,
        ],
      },
      {
        "title": L10n.of(context).chinese_learning,
        "icon": Icons.language_outlined,
        "sections": const ChineseLearningSettings(),
        "subtitles": [
          L10n.of(context).hsk_settings,
          L10n.of(context).dictionary_settings,
          L10n.of(context).text_conversion,
        ],
      },
      {
        "title": L10n.of(context).settings_sync,
        "icon": Icons.sync_outlined,
        "sections": const SyncSetting(),
        "subtitles": [
          L10n.of(context).settings_sync_webdav,
          L10n.of(context).export_and_import,
        ],
      },
      {
        "title": L10n.of(context).settings_narrate,
        "icon": Icons.record_voice_over_outlined,
        "sections": const NarrateSettings(),
        "subtitles": [
          L10n.of(context).settings_narrate_voice,
          L10n.of(context).settings_narrate_voice_model,
        ],
      },
      {
        "title": L10n.of(context).settings_translate,
        "icon": Icons.translate_outlined,
        "sections": const TranslateSettings(),
        "subtitles": [
          L10n.of(context).settings_translate_service,
          L10n.of(context).settings_translate_target_language,
        ],
      },
      {
        "title": L10n.of(context).settings_ai,
        "icon": Icons.smart_toy_outlined,
        "sections": const AISettings(),
        "subtitles": [
          L10n.of(context).settings_ai_services,
          L10n.of(context).settings_ai_prompt,
        ],
      },
      {
        "title": L10n.of(context).storage,
        "icon": Icons.storage_outlined,
        "sections": const StorageSettings(),
        "subtitles": [
          L10n.of(context).storage_info,
          L10n.of(context).storage_data_file_details,
        ],
      },
      {
        "title": L10n.of(context).settings_advanced,
        "icon": Icons.settings_outlined,
        "sections": const AdvancedSetting(),
        "subtitles": [
          L10n.of(context).settings_advanced_log,
          L10n.of(context).settings_advanced_javascript,
        ],
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            AdaptiveIcons.back,
            semanticLabel: 'Back to main settings',
          ),
          tooltip: 'Back',
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          L10n.of(context).settings_moreSettings,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
          ),
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        elevation: DesignSystem.getAdjustedElevation(0.0),
        centerTitle: false,
        titleSpacing: DesignSystem.spaceS,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // Performance optimization: Use cached settings list
          final settings = _cachedSettings ?? [];

          // Ensure settings are cached
          if (_cachedSettings == null) {
            _cacheSettingsList();
          }

          // Performance optimization: Lazy load settings detail
          settingsDetail ??= _getSettingsDetail(0, false);

          /// Lazy load settings detail with caching
          Widget _getSettingsDetail(int index, bool isMobile) {
            if (_settingsSectionCache.containsKey(index)) {
              return _settingsSectionCache[index]!;
            }

            if (index >= settings.length) return const SizedBox.shrink();

            final detail = SettingsPageBody(
              isMobile: isMobile,
              title: settings[index]["title"] as String? ?? '',
              sections:
                  settings[index]["sections"] as Widget? ??
                  const SizedBox.shrink(),
            );

            // Cache the detail widget for performance
            _settingsSectionCache[index] = detail;
            return detail;
          }

          void setDetail(Widget detail, int id) {
            setState(() {
              // Use lazy loading for better performance
              settingsDetail = _getSettingsDetail(id, false);
              selectedIndex = id;
            });
          }

          Widget settingsList(bool isMobile) {
            return SemanticHelpers.scrollableContent(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      DesignSystem.spaceXS, // Reduced for better space usage
                  vertical:
                      DesignSystem
                          .spaceS, // Reduced from spaceM for vertical optimization
                ),
                itemCount: settings.length + 1,
                itemBuilder: (context, index) {
                  if (index == settings.length) {
                    return Padding(
                      padding: EdgeInsets.only(
                        top: DesignSystem.spaceM,
                      ), // Reduced from spaceL
                      child: const About(leadingColor: true),
                    );
                  }
                  return SettingsPageBuilder(
                    isMobile: isMobile,
                    id: index,
                    selectedIndex: selectedIndex,
                    setDetail: setDetail,
                    icon: Icon(
                      settings[index]["icon"] as IconData? ?? Icons.settings,
                      color: Theme.of(context).colorScheme.primary,
                      size: DesignSystem.getAdjustedIconSize(24.0),
                      semanticLabel:
                          '${settings[index]["title"]} settings icon',
                    ),
                    title: settings[index]["title"] as String? ?? '',
                    sections:
                        settings[index]["sections"] as Widget? ??
                        const SizedBox.shrink(),
                    subTitles:
                        (settings[index]["subtitles"] as List<dynamic>?)
                            ?.cast<String>() ??
                        <String>[],
                  );
                },
              ),
              label: 'Settings categories list',
              hint: 'Scroll to see all available settings categories',
            );
          }

          if (constraints.maxWidth > 600) {
            return Row(
              children: [
                Expanded(flex: 1, child: settingsList(false)),
                const VerticalDivider(thickness: 1, width: 1),
                Expanded(flex: 2, child: settingsDetail!),
              ],
            );
          } else {
            return settingsList(true);
          }
        },
      ),
    );
  }
}
